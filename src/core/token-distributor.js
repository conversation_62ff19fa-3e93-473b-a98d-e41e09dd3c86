require('dotenv').config();
const { Connection, PublicKey, Keypair, VersionedTransaction, TransactionMessage, Transaction, ComputeBudgetProgram } = require('@solana/web3.js');
const { PumpSdk } = require('@pump-fun/pump-sdk');
const { getAssociatedTokenAddress, createTransferInstruction, TOKEN_PROGRAM_ID } = require('@solana/spl-token');
const bs58 = require('bs58').default;

class TokenDistributor {
    constructor() {
        this.connection = null;
        this.wallet = null;
        this.tokenMint = null;
        this.rpcUrl = process.env.RPC_URL || 'https://api.mainnet-beta.solana.com';
        this.coinAddress = process.env.COIN;
        this.privateKey = process.env.PK;
        this.maxHunterRewardUSD = parseFloat(process.env.MAX_HUNTER_REWARD_VALUE_USD) || 100;
        this.maxViewerRewardUSD = parseFloat(process.env.MAX_VIEWER_REWARD_VALUE_USD) || 50;
        this.slippageBps = 700; // 7% slippage
        this.maxRetries = 3;
        this.maxTxSize = 800; // Max transaction size in bytes
        this.signatureSize = 64; // Size of signature in bytes
    }

    async init() {
        try {
            console.log('🔗 Initializing Token Distributor...');

            // Initialize connection
            this.connection = new Connection(this.rpcUrl, 'confirmed');
            this.pumpSdk = new PumpSdk(this.connection);

            // Validate environment variables
            if (!this.coinAddress) {
                throw new Error('COIN environment variable not set');
            }
            if (!this.privateKey) {
                throw new Error('PK environment variable not set');
            }

            // Initialize wallet from private key
            const secretKey = bs58.decode(this.privateKey);
            this.wallet = Keypair.fromSecretKey(secretKey);

            // Initialize token mint
            this.tokenMint = new PublicKey(this.coinAddress);

            console.log(`✅ Token Distributor initialized`);
            console.log(`   RPC URL: ${this.rpcUrl}`);
            console.log(`   Wallet: ${this.wallet.publicKey.toString()}`);
            console.log(`   Token Mint: ${this.coinAddress}`);
            console.log(`   Max Hunter Reward: $${this.maxHunterRewardUSD}`);
            console.log(`   Max Viewer Reward: $${this.maxViewerRewardUSD}`);

            return true;
        } catch (error) {
            console.error('❌ Failed to initialize Token Distributor:', error);
            throw error;
        }
    }

    async claimFromVault() {
        try {
            const ix = await this.pumpSdk.collectCoinCreatorFeeInstructions(this.wallet.publicKey)

            // Get recent blockhash and add to tx
            const { blockhash } = await this.connection.getLatestBlockhash();

            const collectFeeTx = new Transaction();

            collectFeeTx.add(...ix);
            collectFeeTx.recentBlockhash = blockhash;
            collectFeeTx.feePayer = this.wallet.publicKey;

            collectFeeTx.sign(this.wallet);

            // convert tx to base64
            const transactionBase64 = Buffer.from(collectFeeTx.serialize()).toString('base64');

            const result = await this.executeTransactionWithRetries(transactionBase64);
            return result;

        } catch (error) {
            console.error('❌ Error claiming from vault:', error);
            throw error;
        }
    }

    async swapSOLToToken(solAmount) {
        try {
            console.log(`🔄 Swapping ${solAmount} SOL to ${this.coinAddress}...`);

            // Get token balance before swap
            const balanceBefore = await this.getTokenBalance();
            console.log(`📊 Token balance before swap: ${(balanceBefore / 1e6).toLocaleString()} tokens`);

            const lamports = Math.floor(solAmount * 1e9); // Convert SOL to lamports

            // Get quote from Jupiter
            const quoteResponse = await this.getJupiterQuote(
                'So11111111111111111111111111111111111111112', // SOL mint
                this.coinAddress,
                lamports
            );

            if (!quoteResponse) {
                throw new Error('Failed to get Jupiter quote');
            }

            console.log(`📊 Quote received: ${quoteResponse.outAmount} tokens for ${solAmount} SOL`);

            // Get swap transaction
            const swapTransaction = await this.getJupiterSwapTransaction(quoteResponse);

            // Deserialize the base64 and sign
            const transactionBuf = Buffer.from(swapTransaction, 'base64');
            const transaction = VersionedTransaction.deserialize(transactionBuf);

            transaction.sign([this.wallet]);
            // Serialize to base64
            const transactionBase64 = Buffer.from(transaction.serialize()).toString('base64');

            const result = await this.executeTransactionWithRetries(transactionBase64);

            console.log(`✅ Swap completed: ${result.txid}`);
            console.log(`🔗 https://solscan.io/tx/${result.txid}`);

            // Wait a moment for the transaction to settle
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Get token balance after swap
            const balanceAfter = await this.getTokenBalance();
            console.log(`📊 Token balance after swap: ${(balanceAfter / 1e6).toLocaleString()} tokens`);

            // Calculate actual tokens received
            const actualTokensReceived = balanceAfter - balanceBefore;
            console.log(`🎯 Actual tokens received: ${(actualTokensReceived / 1e6).toLocaleString()} tokens`);

            return {
                success: true,
                inputAmount: solAmount,
                outputAmount: parseInt(quoteResponse.outAmount),
                balanceBefore: balanceBefore,
                balanceAfter: balanceAfter,
                actualTokensReceived: actualTokensReceived,
                txid: result.txid
            };

        } catch (error) {
            console.error('❌ Error swapping SOL to token:', error);
            throw error;
        }
    }

    async getJupiterQuote(inputMint, outputMint, amount) {
        try {
            const url = `https://lite-api.jup.ag/swap/v1/quote?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amount}&slippageBps=${this.slippageBps}`;

            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Jupiter quote API error: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('❌ Error getting Jupiter quote:', error);
            return null;
        }
    }

    async getJupiterSwapTransaction(quoteResponse) {
        try {
            const response = await fetch('https://lite-api.jup.ag/swap/v1/swap', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    quoteResponse,
                    userPublicKey: this.wallet.publicKey.toString(),
                    wrapAndUnwrapSol: true,
                    dynamicComputeUnitLimit: true,
                    prioritizationFeeLamports: {
                        priorityLevelWithMaxLamports: {
                            maxLamports: 1000000,
                            priorityLevel: "high"
                        }
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`Jupiter swap API error: ${response.status}`);
            }

            const { swapTransaction } = await response.json();
            return swapTransaction;
        } catch (error) {
            console.error('❌ Error getting Jupiter swap transaction:', error);
            throw error;
        }
    }

    async getTokenPrice(tokenMint) {
        try {
            const url = `https://lite-api.jup.ag/price/v3?ids=${tokenMint}`;

            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`Jupiter price API error: ${response.status}`);
            }

            const priceData = await response.json();
            return priceData[tokenMint]?.usdPrice || 0;
        } catch (error) {
            console.error('❌ Error getting token price:', error);
            return 0;
        }
    }

    async getTokenBalance() {
        try {
            // Get our token account address
            const tokenAccount = await getAssociatedTokenAddress(
                this.tokenMint,
                this.wallet.publicKey
            );

            // Get token account info
            const accountInfo = await this.connection.getTokenAccountBalance(tokenAccount);

            if (!accountInfo || !accountInfo.value) {
                console.log('⚠️  Token account not found or empty');
                return 0;
            }

            // Return balance with 6 decimals (always 6 decimals as specified)
            const balance = parseInt(accountInfo.value.amount);
            console.log(`💰 Current token balance: ${(balance / 1e6).toLocaleString()} tokens`);

            return balance;
        } catch (error) {
            console.error('❌ Error getting token balance:', error);
            return 0;
        }
    }

    async executeTransactionWithRetries(transactionBase64) {
        for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
            try {
                console.log(`🔄 Transaction attempt ${attempt}/${this.maxRetries}`);

                // Deserialize transaction
                const transactionBuf = Buffer.from(transactionBase64, 'base64');
                const transaction = VersionedTransaction.deserialize(transactionBuf);

                // Sign transaction
                // transaction.sign([this.wallet]);

                // Get latest blockhash
                const latestBlockHash = await this.connection.getLatestBlockhash('confirmed');

                // Send transaction
                const rawTransaction = transaction.serialize();
                const txid = await this.connection.sendRawTransaction(rawTransaction, {
                    skipPreflight: true,
                    maxRetries: 0
                });

                // Confirm transaction
                await this.connection.confirmTransaction({
                    blockhash: latestBlockHash.blockhash,
                    lastValidBlockHeight: latestBlockHash.lastValidBlockHeight,
                    signature: txid
                });

                return { success: true, txid };

            } catch (error) {
                console.error(`❌ Transaction attempt ${attempt} failed:`, error);

                if (attempt === this.maxRetries) {
                    throw error;
                }

                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
            }
        }
    }

    validateRewardAmounts(recipients, tokenPriceUSD) {
        console.log(`🔍 Validating reward amounts against USD limits...`);
        console.log(`   Token price: $${tokenPriceUSD}`);

        const validatedRecipients = recipients.map(recipient => {
            const { rewardType, tokenAmount } = recipient;
            const usdValue = (tokenAmount / 1e6) * tokenPriceUSD; // Assuming 6 decimals

            const maxUSD = rewardType === 'player' ? this.maxHunterRewardUSD : this.maxViewerRewardUSD;

            if (usdValue > maxUSD) {
                const maxTokenAmount = Math.floor((maxUSD / tokenPriceUSD) * 1e6);
                console.log(`⚠️  Capping ${recipient.username} reward: $${usdValue.toFixed(2)} -> $${maxUSD}`);

                return {
                    ...recipient,
                    tokenAmount: maxTokenAmount,
                    originalTokenAmount: tokenAmount,
                    capped: true,
                    cappedFromUSD: usdValue,
                    cappedToUSD: maxUSD
                };
            }

            return {
                ...recipient,
                capped: false,
                usdValue: usdValue
            };
        });

        const cappedCount = validatedRecipients.filter(r => r.capped).length;
        console.log(`✅ Validation complete: ${cappedCount} rewards capped`);

        return validatedRecipients;
    }

    async createTransferBatches(recipients, checkTokenAccounts = false) {
        console.log(`📦 Creating transfer batches for ${recipients.length} recipients...`);
        console.log(`   Token account validation: ${checkTokenAccounts ? 'ON' : 'OFF'}`);

        const batches = [];
        let currentBatch = [];
        let currentBatchSize = 0;

        // Get our token account address
        const sourceTokenAccount = await getAssociatedTokenAddress(
            this.tokenMint,
            this.wallet.publicKey
        );

        for (const recipient of recipients) {
            try {
                // Validate token account if required
                if (checkTokenAccounts) {
                    const tokenAccountExists = await this.checkTokenAccountExists(recipient.tokenAccountAddress);
                    if (!tokenAccountExists) {
                        console.log(`⚠️  Skipping ${recipient.username}: Token account not found`);
                        batches.push({
                            type: 'failed',
                            recipient: recipient,
                            reason: 'Token account not found',
                            transaction: null
                        });
                        continue;
                    }
                }

                // Create transfer instruction
                const transferInstruction = createTransferInstruction(
                    sourceTokenAccount,
                    new PublicKey(recipient.tokenAccountAddress),
                    this.wallet.publicKey,
                    BigInt(recipient.tokenAmount),
                    [],
                    TOKEN_PROGRAM_ID
                );

                // Estimate instruction size (rough estimate)
                const instructionSize = 64; // Approximate size of transfer instruction

                // Check if adding this instruction would exceed transaction size limit
                if (currentBatchSize + instructionSize + this.signatureSize > this.maxTxSize && currentBatch.length > 0) {
                    currentBatch.unshift(ComputeBudgetProgram.setComputeUnitLimit({ units: 1000000 }));
                    const transaction = await this.createVersionedTransaction(currentBatch);

                    batches.push({
                        type: 'success',
                        recipients: [...currentBatch],
                        transaction: transaction,
                        estimatedSize: currentBatchSize
                    });

                    // Start new batch
                    currentBatch = [];
                    currentBatchSize = 0;
                }

                // Add to current batch
                currentBatch.push({
                    ...recipient,
                    instruction: transferInstruction
                });
                currentBatchSize += instructionSize;

            } catch (error) {
                console.error(`❌ Error processing ${recipient.username}:`, error);
                batches.push({
                    type: 'failed',
                    recipient: recipient,
                    reason: error.message,
                    transaction: null
                });
            }
        }

        // Finalize last batch if it has instructions
        if (currentBatch.length > 0) {
            try {
                const transaction = await this.createVersionedTransaction(currentBatch);
                batches.push({
                    type: 'success',
                    recipients: [...currentBatch],
                    transaction: transaction,
                    estimatedSize: currentBatchSize
                });
            } catch (error) {
                console.error('❌ Error creating final batch transaction:', error);
                // Mark all recipients in this batch as failed
                currentBatch.forEach(recipient => {
                    batches.push({
                        type: 'failed',
                        recipient: recipient,
                        reason: 'Failed to create transaction',
                        transaction: null
                    });
                });
            }
        }

        const successBatches = batches.filter(b => b.type === 'success').length;
        const failedBatches = batches.filter(b => b.type === 'failed').length;

        console.log(`✅ Batch creation complete:`);
        console.log(`   Success batches: ${successBatches}`);
        console.log(`   Failed recipients: ${failedBatches}`);

        return batches;
    }

    async createVersionedTransaction(recipients) {
        try {
            const instructions = recipients.map(r => r.instruction);

            // Get latest blockhash
            const { blockhash } = await this.connection.getLatestBlockhash('confirmed');

            // Create transaction message
            const messageV0 = new TransactionMessage({
                payerKey: this.wallet.publicKey,
                recentBlockhash: blockhash,
                instructions: instructions
            }).compileToV0Message();

            // Create versioned transaction
            const transaction = new VersionedTransaction(messageV0);

            // Check transaction size
            const serializedSize = transaction.serialize().length + this.signatureSize;
            if (serializedSize > this.maxTxSize) {
                throw new Error(`Transaction too large: ${serializedSize} bytes (max: ${this.maxTxSize})`);
            }

            return transaction;
        } catch (error) {
            console.error('❌ Error creating versioned transaction:', error);
            throw error;
        }
    }

    async checkTokenAccountExists(tokenAccountAddress) {
        try {
            const accountInfo = await this.connection.getAccountInfo(new PublicKey(tokenAccountAddress));
            return accountInfo !== null;
        } catch (error) {
            console.error(`❌ Error checking token account ${tokenAccountAddress}:`, error);
            return false;
        }
    }

    async executeBatches(batches) {
        console.log(`🚀 Executing ${batches.length} batches...`);

        const results = [];

        for (let i = 0; i < batches.length; i++) {
            const batch = batches[i];

            if (batch.type === 'failed') {
                results.push(batch);
                continue;
            }

            try {
                console.log(`📤 Executing batch ${i + 1}/${batches.length} (${batch.recipients.length} recipients)`);

                const result = await this.executeTransactionWithRetries(
                    Buffer.from(batch.transaction.serialize()).toString('base64')
                );

                results.push({
                    type: 'success',
                    recipients: batch.recipients,
                    txid: result.txid,
                    estimatedSize: batch.estimatedSize
                });

                console.log(`✅ Batch ${i + 1} completed: ${result.txid}`);
                console.log(`🔗 https://solscan.io/tx/${result.txid}`);

                // Small delay between batches
                if (i < batches.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

            } catch (error) {
                console.error(`❌ Batch ${i + 1} failed:`, error);

                // Mark all recipients in this batch as failed
                batch.recipients.forEach(recipient => {
                    results.push({
                        type: 'failed',
                        recipient: recipient,
                        reason: error.message,
                        transaction: null
                    });
                });
            }
        }

        const successfulTxs = results.filter(r => r.type === 'success').length;
        const failedRecipients = results.filter(r => r.type === 'failed').length;

        console.log(`🎯 Batch execution complete:`);
        console.log(`   Successful transactions: ${successfulTxs}`);
        console.log(`   Failed recipients: ${failedRecipients}`);

        return results;
    }

    async distributeRewards(rewardDistribution, eligiblePlayers, eligibleViewers, database) {
        try {
            console.log('\n🚀 Starting Token Distribution Process...');
            console.log('='.repeat(50));

            let vaultClaimResult;

            // Step 1: Claim from vault
            if (rewardDistribution.useVaultBalance) {
                console.log('🏦 Step 1: Claiming from vault...');
                vaultClaimResult = await this.claimFromVault();
                console.log(`✅ Vault claim result:`, vaultClaimResult);
            }
            else {
                console.log('🏦 Step 1: Skipping vault claim (using external funds)');
                vaultClaimResult = {
                    success: true,
                    amount: 0
                };
            }

            // Step 2: Swap SOL to tokens. Use a try/catch block to retry 5 times. If it fails, throw an error.
            console.log('🔄 Step 2: Swapping SOL to reward tokens...');
            let hasViewers = rewardDistribution.viewerRewards.length > 0;
            // If hasViewers is true, set totalForRewards, else, set totalForPlayers
            let totalSOLToSwap = hasViewers ? rewardDistribution.totalForRewards : rewardDistribution.totalForPlayers;
            let swapResult;
            let swapAttempts = 0;
            while (swapAttempts < 5) {
                try {
                    swapResult = await this.swapSOLToToken(totalSOLToSwap);
                    break;
                } catch (error) {
                    console.error(`❌ Swap attempt ${swapAttempts + 1} failed:`, error);
                    swapAttempts++;
                    if (swapAttempts >= 5) {
                        throw error;
                    }
                }
            }
            console.log(`✅ Swap completed. Actual tokens received: ${(swapResult.actualTokensReceived / 1e6).toLocaleString()}`);

            // Step 3: Get token price
            const tokenPrice = await this.getTokenPrice(process.env.COIN);
            console.log(`💰 Token price: $${tokenPrice}`);

            // Step 4: Convert SOL rewards to token amounts
            const recipients = this.convertSOLRewardsToTokens(
                rewardDistribution,
                eligiblePlayers,
                eligibleViewers,
                swapResult.actualTokensReceived
            );

            // Print each recipient
            recipients.forEach(recipient => {
                console.log(recipient);
            });

            // Step 5: Validate reward amounts against USD limits
            console.log('🔍 Step 5: Validating reward amounts...');
            const validatedRecipients = this.validateRewardAmounts(recipients, tokenPrice);
            const cappedCount = validatedRecipients.filter(r => r.capped).length;
            console.log(`✅ Validation complete: ${cappedCount} rewards were capped`);

            // Step 6: Create transfer batches (first run - no token account validation)
            console.log('📦 Step 6: Creating transfer batches (first run)...');
            const batches = await this.createTransferBatches(validatedRecipients, false);
            console.log(`✅ Created ${batches.length} batches`);

            // Step 7: Execute transfer batches
            console.log('🚀 Step 7: Executing transfer batches...');
            const firstRunResults = await this.executeBatches(batches);

            // Step 8: Second run with token account validation for failed recipients
            console.log('🔍 Step 8: Second run with token account validation...');
            const failedRecipients = validatedRecipients.filter(r =>
                !firstRunResults.some(result =>
                    result.type === 'success' &&
                    result.recipients.some(recipient => recipient.username === r.username)
                )
            );

            let secondRunResults = [];
            if (failedRecipients.length > 0) {
                const secondRunBatches = await this.createTransferBatches(failedRecipients, true);
                secondRunResults = await this.executeBatches(secondRunBatches);
            }

            // Step 9: Store results in database
            console.log('💾 Step 9: Storing distribution results in database...');
            await this.storeDistributionResults(
                database,
                rewardDistribution,
                swapResult,
                tokenPrice,
                validatedRecipients,
                firstRunResults,
                secondRunResults
            );

            // Step 10: Print final summary
            this.printDistributionSummary(
                swapResult,
                validatedRecipients,
                firstRunResults,
                secondRunResults,
                cappedCount
            );

            return {
                success: true,
                vaultClaim: vaultClaimResult,
                swap: swapResult,
                tokenPrice: tokenPrice,
                recipients: validatedRecipients,
                firstRunResults: firstRunResults,
                secondRunResults: secondRunResults,
                summary: {
                    totalRecipients: recipients.length,
                    cappedRewards: cappedCount,
                    actualTokensDistributed: swapResult.actualTokensReceived,
                    firstRunSuccessful: firstRunResults.filter(r => r.type === 'success').length,
                    firstRunFailed: firstRunResults.filter(r => r.type === 'failed').length,
                    secondRunSuccessful: secondRunResults.filter(r => r.type === 'success').length,
                    secondRunFailed: secondRunResults.filter(r => r.type === 'failed').length
                }
            };

        } catch (error) {
            console.error('❌ Token distribution failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    convertSOLRewardsToTokens(rewardDistribution, eligiblePlayers, eligibleViewers, actualTokensReceived) {
        console.log('\n🔄 Converting SOL rewards to token amounts...');

        const recipients = [];
        const totalSOLForRewards = rewardDistribution.totalForRewards;

        // Convert player rewards
        for (const player of rewardDistribution.playerRewards) {
            const tokenAmount = Math.floor((player.rewardSOL / totalSOLForRewards) * actualTokensReceived);

            recipients.push({
                username: player.username,
                userAddress: player.user_address,
                tokenAccountAddress: player.tokenAccountAddress,
                rewardType: 'player',
                tokenAmount: tokenAmount,
                originalSOLAmount: player.rewardSOL,
                rank: player.rank,
                moves: player.moves
            });
        }

        // Convert viewer rewards
        for (const viewer of rewardDistribution.viewerRewards) {
            const tokenAmount = Math.floor((viewer.rewardSOL / totalSOLForRewards) * actualTokensReceived);

            recipients.push({
                username: viewer.username,
                userAddress: viewer.user_address,
                tokenAccountAddress: viewer.tokenAccountAddress,
                rewardType: 'viewer',
                tokenAmount: tokenAmount,
                originalSOLAmount: viewer.rewardSOL,
                rank: viewer.rank,
                viewingSessions: viewer.viewing_sessions
            });
        }

        console.log(`✅ Converted ${recipients.length} SOL rewards to token amounts`);
        console.log(`   Players: ${recipients.filter(r => r.rewardType === 'player').length}`);
        console.log(`   Viewers: ${recipients.filter(r => r.rewardType === 'viewer').length}`);

        return recipients;
    }

    async storeDistributionResults(database, _rewardDistribution, swapResult, tokenPrice, recipients, firstRunResults, secondRunResults) {
        try {
            console.log('💾 Storing token distribution results...');

            // Create a new table for token distributions if it doesn't exist
            await database.runQuery(`
                CREATE TABLE IF NOT EXISTS token_distributions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    distribution_timestamp DATETIME NOT NULL,
                    vault_claim_txid TEXT,
                    swap_txid TEXT,
                    sol_amount_swapped REAL NOT NULL,
                    tokens_received REAL NOT NULL,
                    token_price_usd REAL NOT NULL,
                    total_recipients INTEGER NOT NULL,
                    successful_transfers INTEGER NOT NULL,
                    failed_transfers INTEGER NOT NULL,
                    capped_rewards INTEGER NOT NULL,
                    balance_before_swap REAL NOT NULL,
                    balance_after_swap REAL NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // Create token transfer results table
            await database.runQuery(`
                CREATE TABLE IF NOT EXISTS token_transfer_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    distribution_id INTEGER,
                    username TEXT NOT NULL,
                    user_address TEXT,
                    token_account_address TEXT,
                    reward_type TEXT NOT NULL CHECK (reward_type IN ('player', 'viewer')),
                    token_amount REAL NOT NULL,
                    original_sol_amount REAL NOT NULL,
                    usd_value REAL,
                    was_capped BOOLEAN DEFAULT FALSE,
                    transfer_status TEXT NOT NULL CHECK (transfer_status IN ('success', 'failed')),
                    txid TEXT,
                    failure_reason TEXT,
                    rank INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (distribution_id) REFERENCES token_distributions (id)
                )
            `);

            // Insert main distribution record
            const distributionResult = await database.runQuery(`
                INSERT INTO token_distributions (
                    distribution_timestamp, vault_claim_txid, swap_txid,
                    sol_amount_swapped, tokens_received, token_price_usd,
                    total_recipients, successful_transfers, failed_transfers,
                    capped_rewards, balance_before_swap, balance_after_swap
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                new Date().toISOString(),
                swapResult.vaultClaimTxid || null,
                swapResult.txid,
                swapResult.inputAmount,
                swapResult.actualTokensReceived / 1e6, // Store in human-readable format
                tokenPrice,
                recipients.length,
                [...firstRunResults, ...secondRunResults].filter(r => r.type === 'success').length,
                [...firstRunResults, ...secondRunResults].filter(r => r.type === 'failed').length,
                recipients.filter(r => r.capped).length,
                swapResult.balanceBefore / 1e6,
                swapResult.balanceAfter / 1e6
            ]);

            const distributionId = distributionResult.lastID;

            // Store individual transfer results
            for (const recipient of recipients) {
                // Determine transfer status
                let transferStatus = 'failed';
                let txid = null;
                let failureReason = null;

                // Check first run results
                const firstRunSuccess = firstRunResults.find(result =>
                    result.type === 'success' &&
                    result.recipients.some(r => r.username === recipient.username)
                );

                if (firstRunSuccess) {
                    transferStatus = 'success';
                    txid = firstRunSuccess.txid;
                } else {
                    // Check second run results
                    const secondRunSuccess = secondRunResults.find(result =>
                        result.type === 'success' &&
                        result.recipients.some(r => r.username === recipient.username)
                    );

                    if (secondRunSuccess) {
                        transferStatus = 'success';
                        txid = secondRunSuccess.txid;
                    } else {
                        // Find failure reason
                        const failedResult = [...firstRunResults, ...secondRunResults].find(result =>
                            result.type === 'failed' &&
                            (result.recipient?.username === recipient.username ||
                                result.recipients?.some(r => r.username === recipient.username))
                        );
                        failureReason = failedResult?.reason || 'Unknown failure';
                    }
                }

                await database.runQuery(`
                    INSERT INTO token_transfer_results (
                        distribution_id, username, user_address, token_account_address,
                        reward_type, token_amount, original_sol_amount, usd_value,
                        was_capped, transfer_status, txid, failure_reason, rank
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    distributionId,
                    recipient.username,
                    recipient.userAddress,
                    recipient.tokenAccountAddress,
                    recipient.rewardType,
                    recipient.tokenAmount / 1e6, // Store in human-readable format
                    recipient.originalSOLAmount,
                    recipient.usdValue || (recipient.tokenAmount / 1e6) * tokenPrice,
                    recipient.capped || false,
                    transferStatus,
                    txid,
                    failureReason,
                    recipient.rank
                ]);
            }

            console.log(`✅ Stored token distribution results with ID: ${distributionId}`);
            return distributionId;

        } catch (error) {
            console.error('❌ Error storing distribution results:', error);
            throw error;
        }
    }

    printDistributionSummary(swapResult, recipients, firstRunResults, secondRunResults, cappedCount) {
        console.log('\n🎉 TOKEN DISTRIBUTION COMPLETE!');
        console.log('='.repeat(80));

        console.log(`\n💰 SWAP SUMMARY:`);
        console.log(`   SOL Swapped: ${swapResult.inputAmount} SOL`);
        console.log(`   Tokens Received: ${(swapResult.actualTokensReceived / 1e6).toLocaleString()} tokens`);
        console.log(`   Balance Before: ${(swapResult.balanceBefore / 1e6).toLocaleString()} tokens`);
        console.log(`   Balance After: ${(swapResult.balanceAfter / 1e6).toLocaleString()} tokens`);

        const allResults = [...firstRunResults, ...secondRunResults];
        const successfulTxs = allResults.filter(r => r.type === 'success').length;
        const failedRecipients = allResults.filter(r => r.type === 'failed').length;

        console.log(`\n📊 DISTRIBUTION SUMMARY:`);
        console.log(`   Total Recipients: ${recipients.length}`);
        console.log(`   Players: ${recipients.filter(r => r.rewardType === 'player').length}`);
        console.log(`   Viewers: ${recipients.filter(r => r.rewardType === 'viewer').length}`);
        console.log(`   Successful Transfers: ${successfulTxs}`);
        console.log(`   Failed Transfers: ${failedRecipients}`);
        console.log(`   Capped Rewards: ${cappedCount}`);

        console.log(`\n🔗 TRANSACTION LINKS:`);
        allResults.filter(r => r.type === 'success').forEach((result, index) => {
            console.log(`   TX ${index + 1}: https://solscan.io/tx/${result.txid} (${result.recipients.length} recipients)`);
        });

        console.log('\n✅ All distribution data stored in database!');
        console.log('='.repeat(80));
    }
}

module.exports = TokenDistributor;
